{"name": "@perkd/multitenant-context", "version": "0.6.3", "description": "Multitenant context (drop-in replacement for loopback-context)", "author": "<EMAIL>", "private": true, "engines": {"node": ">=20"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "npx tsc", "prestart": "npm run build", "start": "node dist/index.js", "update": "ncu -u && rm -rf node_modules/ yarn.lock && yarn install", "pretest": "npm run build", "test": "node --test --require ts-node/register tests/**/*.test.ts", "test-one": "node --test --require ts-node/register", "reinstall": "rm -rf node_modules/ yarn.lock; yarn; rm -rf dist/ tsconfig.tsbuildinfo; tsc", "release": "rm -rf dist/ tsconfig.tsbuildinfo && tsc && rm -rf node_modules/ yarn.lock && yarn workspaces focus --production"}, "repository": {"type": "git", "url": "git+ssh://**************/perkd/multitenant-context.git"}, "bugs": {"url": "https://github.com/perkd/multitenant-context/issues"}, "homepage": "https://github.com/perkd/multitenant-context#readme", "files": ["README.md", "dist", "!*/__tests__", "!tsconfig.tsbuildinfo"], "dependencies": {"@crm/loopback": "github:perkd/crm-loopback#semver:^0.8.2", "@perkd/errors": "github:perkd/errors#semver:^0.5.1", "@perkd/utils": "github:perkd/utils#semver:^2.0.5", "@perkd/wallet": "github:perkd/wallet#semver:^0.5.0", "tslib": "^2.8.1"}, "devDependencies": {"@perkd/eslint-config": "github:Perkd-X/eslint-config#semver:^3.1.3", "@types/async-lock": "^1", "@types/node": "^24.0.4", "async-lock": "^1.4.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "packageManager": "yarn@4.9.2"}