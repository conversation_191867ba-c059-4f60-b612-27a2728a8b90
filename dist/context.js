"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const node_async_hooks_1 = require("node:async_hooks");
const node_domain_1 = tslib_1.__importDefault(require("node:domain"));
const utils_1 = require("@perkd/utils");
const errors_1 = require("@perkd/errors");
const utils_2 = require("./utils");
const types_1 = require("./types");
// environment variables
const { CONTEXT_MODE, DISABLE_ASYNC_CONTEXT, PERKD_SECRET_KEY = 'perkd-secret-key' } = process.env;
const { Jwt } = utils_1.Security, ENGLISH = 'en', EXPIRY = 'exp'; // jwt
class MultitenantContext {
    static _instance;
    als;
    activeDomains;
    metrics;
    enabled;
    _activeContextsRegistry;
    static withContext(context, fn) {
        const instance = new MultitenantContext();
        return instance.runInContext(context, fn);
    }
    constructor() {
        if (MultitenantContext._instance) {
            return MultitenantContext._instance;
        }
        this.als = new node_async_hooks_1.AsyncLocalStorage();
        this.activeDomains = new WeakMap();
        this.metrics = {
            domainFallbacks: 0,
            strictAccess: 0,
            total: 0
        };
        this.enabled = DISABLE_ASYNC_CONTEXT !== 'true';
        MultitenantContext._instance = Object.freeze(this);
    }
    // ---  loopback-context compatibility
    createContext(initialValue = {}) {
        const { requestId = this.generateRequestId() } = initialValue;
        return {
            ...initialValue,
            requestId,
            get: (key) => this.get(key),
            set: (key, value) => this.set(key, value),
            _domain: this._createDomain(initialValue)
        };
    }
    releaseContext(context) {
        if (!context)
            return;
        try {
            // Clean up domain-specific resources if domain exists
            if (context._domain) {
                // Remove from active domains map
                this.activeDomains.delete(context._domain);
                // Remove all listeners, not just error
                context._domain.removeAllListeners();
                // Break circular references if any exist
                if (context._domain._loopbackContext === context) {
                    delete context._domain._loopbackContext;
                }
                // Remove domain reference from context
                delete context._domain;
            }
            // Clean up function references that could cause memory leaks
            // These create closures that capture 'this' and prevent garbage collection
            if (context.get)
                delete context.get;
            if (context.set)
                delete context.set;
            // Clean up any MongoDB connections or other resources
            // that might be attached to this context
            // Optional debug logging
            // if (context.requestId) {
            // 	console.debug(`Released context for request ${context.requestId}`);
            // }
        }
        catch (err) {
            console.error('Error releasing context:', err);
        }
    }
    getCurrentContext() {
        const { metrics } = this, ctx = this.als.getStore();
        metrics.total++;
        if (ctx) {
            metrics.strictAccess++;
            return ctx;
        }
        const { stack = '' } = new Error(), match = utils_2.EXTRACT_CALLER.exec(stack), caller = match ? `[${match?.[1]} at ${match?.[2]}]` : stack;
        // match[1] is the extracted function name
        // match[2] is the extracted file, line, and column information
        metrics.domainFallbacks++;
        console.warn(`Domain fallback (${metrics.domainFallbacks}): ${metrics.domainFallbacks / metrics.total * 100}% ${caller}`);
        return process.domain?._loopbackContext || {};
    }
    get(key) {
        return this.context?.[key];
    }
    set(key, value) {
        if (this.context) {
            this.context[key] = value;
            return value;
        }
    }
    // ---  accessors
    get context() {
        return this.getCurrentContext();
    }
    get tenant() {
        return this.context?.[types_1.TENANT] || types_1.TRAP;
    }
    set tenant(code) {
        if (this.context) {
            this.context[types_1.TENANT] = code.toLowerCase();
        }
    }
    get accessToken() {
        return this.context?.[types_1.ACCESS_TOKEN] || '';
    }
    set accessToken(token) {
        if (this.context) {
            this.context[types_1.ACCESS_TOKEN] = token;
        }
    }
    get timezone() {
        return this.context?.[types_1.TIMEZONE];
    }
    set timezone(timezone) {
        if (this.context) {
            this.context[types_1.TIMEZONE] = timezone;
        }
    }
    get user() {
        return this.context?.[types_1.USER];
    }
    set user(user) {
        if (this.context) {
            this.context[types_1.USER] = user;
        }
    }
    get origin() {
        return this.context?.[types_1.ORIGIN];
    }
    set origin(origin) {
        if (this.context) {
            this.context[types_1.ORIGIN] = origin;
        }
    }
    get idempotencyKey() {
        return this.get(types_1.IDEMPOTENCY_KEY) || (0, utils_1.shortId)();
    }
    set idempotencyKey(value) {
        if (this.context) {
            this.context[types_1.IDEMPOTENCY_KEY] = value;
        }
    }
    //  Perkd App specific
    get appContext() {
        const { location: loc, user, cardProfile: card, installation } = this, { spot } = loc ?? {}, { id: userId, staffId, username: userName } = user ?? {}, staff = staffId ? { id: staffId, userId, userName } : undefined, { id, type, name } = spot ?? {}, location = { id, type, name };
        return { staff, user, card, location, installation };
    }
    get accountId() {
        const { accountId = null } = this.user ?? {};
        return accountId;
    }
    /**
     * Get installation & user from Perkd App
     * @return	{Object} { ...install, ...user }
     * 1. install - decoded JWT perkd-install header. (see installation/docs/perkd-install.json)
     * 2. user - user object in decoded JWT Authorization header. (see installation/docs/Authorization.json)
     */
    get installation() {
        const { user } = this, install = this.get(types_1.INSTALL), { id: userId } = user ?? {}, { personId, accountId } = user ?? {};
        if (!install)
            return {};
        if (!install.id) {
            const err = new Error('header_missing_install');
            console.error('getInstall', { install, userId, personId, accountId, err });
        }
        // CRM: inject userId (personId & accountId N.A) (placeId already in install)
        // Perkd: inject personId, accountId (userId N.A)
        return { ...install, userId, personId, accountId };
    }
    set installation(installation) {
        if (this.context) {
            this.context[types_1.INSTALL] = installation;
        }
    }
    get cardProfile() {
        return this.get(types_1.CARD) || {};
    }
    set cardProfile(card) {
        if (this.context) {
            this.context[types_1.CARD] = card;
        }
    }
    get location() {
        return this.get(types_1.LOCATION) || {};
    }
    set location(location) {
        if (this.context) {
            this.context[types_1.LOCATION] = location;
        }
    }
    get language() {
        return this.get(types_1.LANGUAGE) || ENGLISH;
    }
    set language(language) {
        if (this.context) {
            this.context[types_1.LANGUAGE] = language;
        }
    }
    // ---  Extended methods
    /**
     * Set the context values
     * @param tenant - tenant code
     * @param [user] - CRM user or App user
     * @param [timezone] - timezone of tenant
     * @param [origin] - origin of request
     */
    setValues(tenant, user, timezone, origin) {
        this.tenant = tenant;
        this.user = user;
        this.timezone = timezone;
        if (origin)
            this.origin = origin;
    }
    /**
     * Set the context with the token, stored in the context:
     * 	tenant - tenant code
     * 	user - username
     * 	timezone - timezone of tenant
     * 	exp - expiration of token
     * @param accessToken - jwt token
     * @param secretKey - jwt secret key
     * @param options - allowExpired
     * @returns - payload or error
     */
    setWithToken(accessToken, secretKey, options = {}) {
        if (!accessToken) {
            return new errors_1.TenantIsolationError('Authentication: failed to get token');
        }
        this.accessToken = accessToken;
        let decodedJWT;
        try {
            const jwt = new Jwt(secretKey);
            if (jwt.verify(accessToken)) {
                decodedJWT = jwt.decode(accessToken);
            }
            else {
                return new errors_1.TenantIsolationError('Authentication: invalid token: ' + accessToken);
            }
        }
        catch (err) {
            return new errors_1.TenantIsolationError(`Authentication error: ${err.message}`);
        }
        try {
            const { allowExpired } = options, payload = (typeof decodedJWT.payload === 'string')
                ? JSON.parse(decodedJWT.payload)
                : decodedJWT.payload, { user, exp } = payload, { code = '', timezone } = payload.tenant ?? {}, tenant = code.toLowerCase(), result = { tenant, user, timezone, exp };
            if (!allowExpired && payload[EXPIRY]) {
                const now = Math.floor(Date.now() / 1000);
                if (payload[EXPIRY] <= now) {
                    return new errors_1.TenantIsolationError('Authentication: token has expired');
                }
            }
            if (!tenant) {
                return new errors_1.TenantIsolationError('Authentication: missing Tenant Code');
            }
            this.setValues(tenant, user, timezone);
            return result;
        }
        catch (err) {
            return new errors_1.TenantIsolationError(`Token parsing error: ${err.message}`);
        }
    }
    bindEmitter(emitter) {
        const d = process.domain;
        if (d && d._loopbackContext) {
            d.add(emitter);
        }
    }
    generateAccessToken(payload = {}, secret = PERKD_SECRET_KEY) {
        if (!payload.tenant) {
            payload.tenant = {
                code: this.tenant,
                timezone: this.timezone
            };
        }
        const jwt = new Jwt(secret);
        return jwt.encode(payload);
    }
    generateRequestId() {
        return Date.now().toString(36) + Math.random().toString(36).slice(2);
    }
    // ---  Execution in Context
    async runInContext(context, fn) {
        // Validate tenant exists using Symbol key
        if (CONTEXT_MODE === 'strict' && !context[types_1.TENANT]) {
            throw new errors_1.TenantIsolationError('Missing tenant in context');
        }
        const enhancedContext = {
            [types_1.TENANT]: context[types_1.TENANT], // Use Symbol key
            ...context,
            get: (key) => this.get(key),
            set: (key, value) => this.set(key, value)
        };
        try {
            if (CONTEXT_MODE === 'strict') {
                return this.als.run(enhancedContext, () => {
                    enhancedContext[types_1.TENANT] = context[types_1.TENANT];
                    return fn();
                });
            }
            // Legacy domain-based handling
            const d = this._createDomain(enhancedContext);
            enhancedContext._domain = d;
            return await this.als.run(enhancedContext, () => d.run(() => {
                d._loopbackContext = enhancedContext;
                return fn();
            }));
        }
        catch (err) {
            console.error('Error in runInContext:', err);
            throw err;
        }
        finally {
            // Always release context regardless of mode to prevent memory leaks
            this.releaseContext(enhancedContext);
            this.releaseContext(context);
            // Additional cleanup for enhanced context to break circular references
            delete enhancedContext.get;
            delete enhancedContext.set;
            // Clear all enumerable properties to ensure no lingering references
            Object.keys(enhancedContext).forEach(key => {
                if (key !== types_1.TENANT.toString()) { // Preserve tenant symbol if needed elsewhere
                    delete enhancedContext[key];
                }
            });
        }
    }
    async runAsTenant(tenant, fn, connectionManager, options = {}) {
        if (!tenant) {
            throw new errors_1.TenantIsolationError('runAsTenant: missing Tenant Code');
        }
        const baseContext = this.context || {}, tenantConfig = typeof tenant === 'string'
            ? { id: tenant, ...options }
            : tenant, tenantCode = tenantConfig.id, ctx = {
            ...baseContext,
            [types_1.TENANT]: tenantCode,
            tenantConfig: { ...baseContext.tenantConfig, ...tenantConfig }
        };
        // Store original context values to restore in case of error
        const originalContext = {
            tenant: this.tenant,
            accessToken: this.accessToken,
            user: this.user,
            timezone: this.timezone
        };
        try {
            return await this.runInContext(ctx, async () => {
                let session = null;
                try {
                    if (connectionManager) {
                        await connectionManager.ensureConnection(tenantCode);
                        session = await connectionManager.startSession(tenantCode);
                    }
                    return await fn();
                }
                catch (err) {
                    console.error('Error during tenant operation:', err);
                    throw err;
                }
                finally {
                    if (session) {
                        try {
                            await connectionManager.withTransaction(tenantCode, async () => {
                                if (session.hasActiveTransaction?.()) {
                                    await session.abortTransaction?.();
                                }
                            });
                        }
                        catch (err) {
                            console.error('Error cleaning up session:', err);
                        }
                    }
                }
            });
        }
        catch (err) {
            // Restore original context values on error
            try {
                this.tenant = originalContext.tenant;
                this.accessToken = originalContext.accessToken;
                this.user = originalContext.user;
                this.timezone = originalContext.timezone;
            }
            catch (restoreErr) {
                console.error('Error restoring original context:', restoreErr);
            }
            throw err;
        }
    }
    /**
     * Execute with role privileges
     * @param user
     * @param role
     * @param fn
     * @param tenantCode
     */
    async runWithPrivilege(user, role, fn, tenantCode) {
        const { accessToken: oToken, tenant: oTenant } = this;
        try {
            const payload = {
                user: { ...user, roles: [role] },
                tenant: { code: tenantCode || oTenant }
            }, token = this.generateAccessToken(payload);
            if (tenantCode) {
                this.tenant = tenantCode;
                // FIXME await cacheDataSource(app, code)
            }
            this.accessToken = token;
            const res = await fn();
            return res;
        }
        catch (err) {
            throw err instanceof Error ? err : new Error(String(err));
        }
        finally {
            this.accessToken = oToken;
            if (tenantCode && oTenant)
                this.tenant = oTenant;
        }
    }
    _createDomain(context) {
        const d = node_domain_1.default.create();
        d._loopbackContext = context;
        this.activeDomains.set(d, true);
        d.on('error', (err) => this._handleDomainError(err, d));
        return d;
    }
    _handleDomainError(err, d) {
        console.error('Domain error:', err);
        this.activeDomains.delete(d);
    }
    trackActiveContext(context) {
        // Keep track of contexts in a Set or Array
        if (!this._activeContextsRegistry) {
            this._activeContextsRegistry = new Set();
        }
        this._activeContextsRegistry.add(context);
    }
    releaseAllContexts() {
        // If we're tracking contexts separately
        if (this._activeContextsRegistry) {
            const contexts = Array.from(this._activeContextsRegistry);
            contexts.forEach(context => {
                this.releaseContext(context);
                this._activeContextsRegistry?.delete(context);
            });
        }
        console.info('All active contexts released');
    }
}
const Context = new MultitenantContext();
exports.default = Context;
//# sourceMappingURL=context.js.map