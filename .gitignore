## Source code ##
# Common modules
server/lib/clients
client

## Build artifacts ##
# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Users Environment Variables
.lock-wscript

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/Release

# Dependency directory
# Commenting this out is preferred by some people, see
# https://www.npmjs.org/doc/misc/npm-faq.html#should-i-check-my-node_modules-folder-into-git-
node_modules

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
.nyc_output
coverage


## Runtime files ##
# Runtime data
pids
*.pid
*.seed

# File storage
db.json


## Hidden files ##
# Git
.git

# Dotenv
.env
.env.*

# MacOS DS_Store file
.DS_Store

# Yarn
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
