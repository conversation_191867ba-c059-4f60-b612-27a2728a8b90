import { describe, it, beforeEach, afterEach } from 'node:test'
import assert from 'node:assert'
import AsyncLock from 'async-lock'
import Context from '../src/context'
import { TENANT } from '../src/types'

describe('Queue Tenant Context', () => {
  const TENANT_A = 'tenant-a'
  const TEST_KEY = 'test-key'
  let lock: AsyncLock

  beforeEach(() => {
    lock = new AsyncLock()
    process.env.CONTEXT_MODE = 'strict'
    process.env.DISABLE_ASYNC_CONTEXT = 'false'
    // Initialize base context to prevent domain fallback
    Context.createContext({ [TENANT]: 'initial' })
  })

  it('should maintain tenant context when using runAsTenant in queue', async () => {
    return Context.runInContext({ [TENANT]: 'base' }, async () => {
      const result = await lock.acquire(TEST_KEY, () => 
        Context.runAsTenant(TENANT_A, () => {
          return Context.tenant
        })
      )
      assert.strictEqual(result, TENANT_A)
    })
  })

  it('should properly handle errors from runAsTenant in queue', async () => {
    console.log('>>>>>> It is correct to see error message "Error during tenant operation" , normal error log')
    await assert.rejects(
      async () => {
    await Context.runInContext({ [TENANT]: 'base' }, async () => {
      await lock.acquire(TEST_KEY, async () => {
        await Context.runAsTenant(TENANT_A, () => {
          throw new Error('Test error')
        })
      })
    })
      },
      {
        name: 'Error',
        message: 'Test error'
      }
    )
  })

  it('should maintain tenant isolation when sharing same queue', async () => {
    const TENANT_B = 'tenant-b'
    const results: string[] = []

    // First tenant operation
    await Context.runInContext({ [TENANT]: TENANT_A }, async () => {
      await lock.acquire(TEST_KEY, async () => {
        await delay(50) // Simulate some work
        await Context.runAsTenant(TENANT_A, () => {
          results.push(Context.tenant)
        })
      })
    })

    // Second tenant operation using same queue/key
    await Context.runInContext({ [TENANT]: TENANT_B }, async () => {
      await lock.acquire(TEST_KEY, async () => {
        await Context.runAsTenant(TENANT_B, () => {
          results.push(Context.tenant)
        })
      })
    })

    // Verify tenants were properly isolated
    assert.deepStrictEqual(results, [TENANT_A, TENANT_B])
  })

  function delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}) 