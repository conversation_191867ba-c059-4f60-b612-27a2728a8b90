{"$schema": "http://json.schemastore.org/tsconfig", "compilerOptions": {"module": "commonjs", "moduleResolution": "node", "target": "es2022", "lib": ["es2022"], "outDir": "dist", "rootDir": "src", "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "strictPropertyInitialization": false, "esModuleInterop": true, "incremental": true, "composite": true, "sourceMap": true, "declaration": true, "importHelpers": true}, "include": ["src/**/*", "src/*.json"], "exclude": ["node_modules/**", "tests", "**/*.d.ts"]}