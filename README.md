# @perkd/multitenant-context

A drop-in replacement for loopback-context with enhanced multitenant support for Node.js applications.

[![Version](https://img.shields.io/badge/version-0.6.2-blue.svg)](https://github.com/perkd/multitenant-context)
[![Node](https://img.shields.io/badge/node->=20-green.svg)](https://nodejs.org/)

## Overview

`multitenant-context` provides a robust solution for managing context data across asynchronous operations in Node.js applications, with a focus on multitenancy. It maintains tenant isolation while preserving context through async operations, making it ideal for SaaS applications and microservices architectures.

This library is a modern replacement for the legacy `loopback-context` module, built with TypeScript and leveraging Node.js AsyncLocalStorage for reliable context propagation.

### Architecture

```mermaid
flowchart TD
    subgraph "Request Processing"
        A[API Request] --> B[Context Middleware]
        B --> C{JWT Token?}
        C -->|Yes| D[Extract Token Data]
        C -->|No| E[Create Empty Context]
        D --> F[Create Context]
        E --> F
        F --> G[AsyncLocalStorage]
    end

    subgraph "Tenant A Context"
        G --> H1[Business Logic]
        H1 --> I1[Database Operations]
        H1 --> J1[External API Calls]
        H1 --> K1[Async Operations]

        style H1 fill:#c30,stroke:#333,color:white
        style I1 fill:#c30,stroke:#333,color:white
        style J1 fill:#c30,stroke:#333,color:white
        style K1 fill:#c30,stroke:#333,color:white
    end

    subgraph "Tenant B Context"
        G --> H2[Business Logic]
        H2 --> I2[Database Operations]
        H2 --> J2[External API Calls]
        H2 --> K2[Async Operations]

        style H2 fill:#36c,stroke:#333,color:white
        style I2 fill:#36c,stroke:#333,color:white
        style J2 fill:#36c,stroke:#333,color:white
        style K2 fill:#36c,stroke:#333,color:white
    end

    style Tenant A Context fill:#c30,stroke:#333,stroke-width:2px,color:white
    style Tenant B Context fill:#36c,stroke:#333,stroke-width:2px,color:white
```

### Context Flow

```mermaid
sequenceDiagram
    participant Client
    participant Middleware
    participant Context
    participant AsyncOps as Async Operations
    participant DB as Database

    Client->>Middleware: Request with tenant info
    Middleware->>Context: Create context with tenant
    Context->>Context: Store in AsyncLocalStorage
    Middleware->>AsyncOps: Execute business logic
    AsyncOps->>Context: Access tenant context
    AsyncOps->>DB: Perform DB operations
    DB->>AsyncOps: Return results
    AsyncOps->>Client: Response with tenant data

    Note over Context,AsyncOps: Context is preserved<br>across async boundaries
```

## Features

- **Tenant Isolation**: Strict tenant context boundaries to prevent data leakage between tenants
- **Async Context Preservation**: Maintains context through async operations, promises, and callbacks
- **JWT Integration**: Built-in JWT token generation and validation for authentication
- **TypeScript Support**: Full TypeScript definitions for improved developer experience
- **Legacy Compatibility**: Drop-in replacement for loopback-context with backward compatibility
- **Connection Management**: Integration with database connection managers for tenant-specific transactions
- **Metrics**: Built-in tracking of context usage patterns

## Installation

```bash
yarn add @perkd/multitenant-context
```

## Basic Usage

```typescript
import { Context } from '@perkd/multitenant-context';
import { TENANT, USER } from '@perkd/multitenant-context';

// Create a context with tenant information
const context = Context.createContext({
  [TENANT]: 'my-tenant',
  [USER]: { id: 'user-123', username: 'john.doe' }
});

// Run code within a specific tenant context
await Context.runInContext({
  [TENANT]: 'my-tenant',
  [USER]: { id: 'user-123', username: 'john.doe' }
}, async () => {
  // Context is available here and in all async operations
  console.log(Context.tenant); // 'my-tenant'

  await someAsyncOperation();
  // Context is still preserved here
  console.log(Context.tenant); // 'my-tenant'
});
```

## Advanced Usage

### Tenant Isolation with Database Transactions

```typescript
import { Context } from '@perkd/multitenant-context';
import { connectionManager } from './your-db-connection';

// Run operations in a specific tenant with database transaction support
await Context.runAsTenant('tenant-a', async () => {
  // Operations here run within tenant-a context and transaction
  const data = await fetchSomeTenantData();
  await updateTenantData(data);
}, connectionManager);
```

### JWT Token Management

```typescript
import { Context } from '@perkd/multitenant-context';

// Generate a JWT token with current context
const token = Context.generateAccessToken();

// Set context from a JWT token
const result = Context.setContextWithToken(token, 'your-secret-key');
if (result instanceof Error) {
  console.error('Token validation failed:', result.message);
} else {
  console.log('Context set from token:', Context.tenant);
}
```

### Running with Elevated Privileges

```typescript
import { Context } from '@perkd/multitenant-context';

// Execute a function with elevated privileges
await Context.runWithPrivilege(
  { id: 'admin-user', username: 'admin' },
  'admin',
  async () => {
    // Operations here run with admin privileges
    await performAdminOperation();
  }
);
```

## Tenant Isolation Concept

The core concept of this library is maintaining strict tenant isolation throughout asynchronous operations.

```mermaid
graph TD
    ALS[AsyncLocalStorage]

    subgraph "Tenant A Context"
        A1[API Endpoint]
        B1[Business Logic]
        B1 --> D1[External API Call]
        B1 --> E1[Async Operation]
        B1 --> C1[Database Query]

        style A1 fill:#c30,stroke:#333,color:white
        style B1 fill:#c30,stroke:#333,color:white
        style C1 fill:#c30,stroke:#333,color:white
        style D1 fill:#c30,stroke:#333,color:white
        style E1 fill:#c30,stroke:#333,color:white
    end

    subgraph "Tenant B Context"
        A2[API Endpoint]
        B2[Business Logic]
        B2 --> C2[Database Query]
        B2 --> E2[Async Operation]
        B2 --> D2[External API Call]

        style A2 fill:#36c,stroke:#333,color:white
        style B2 fill:#36c,stroke:#333,color:white
        style C2 fill:#36c,stroke:#333,color:white
        style D2 fill:#36c,stroke:#333,color:white
        style E2 fill:#36c,stroke:#333,color:white
    end

    subgraph "Database"
        G1[Tenant A Data]
        G2[Tenant B Data]

        style G1 fill:#c30,stroke:#333,color:white
        style G2 fill:#36c,stroke:#333,color:white
    end

    A1 --> ALS
    A2 --> ALS
    ALS --> B1
    ALS --> B2
    C1 --> G1
    C2 --> G2
```

## Configuration

The library can be configured using environment variables:

- `CONTEXT_MODE`: Set to `strict` to enforce tenant isolation (recommended) or `legacy` for backward compatibility
- `DISABLE_ASYNC_CONTEXT`: Set to `true` to disable AsyncLocalStorage and fall back to domain-based context
- `PERKD_SECRET_KEY`: Default secret key for JWT operations (can be overridden in method calls)

## API Reference

### Context Class

The main class providing context management functionality.

#### Static Methods

- `createContext(initialValue?)`: Creates a new context object
- `runInContext(context, fn)`: Runs a function within the specified context
- `runAsTenant(tenantCode, fn, connectionManager?)`: Runs a function within a specific tenant context
- `runWithPrivilege(user, role, fn, tenantCode?)`: Runs a function with elevated privileges
- `withContext(context, fn)`: Legacy compatibility method for running in context

#### Instance Properties

- `tenant`: Get/set the current tenant code
- `user`: Get/set the current user object
- `timezone`: Get/set the current timezone
- `origin`: Get/set the request origin
- `idempotencyKey`: Get/set the idempotency key
- `installation`: Get/set the installation information
- `cardProfile`: Get/set the card profile
- `location`: Get/set the location information
- `language`: Get/set the language preference

#### Instance Methods

- `getCurrentContext()`: Gets the current context object
- `get(key)`: Gets a value from the current context
- `set(key, value)`: Sets a value in the current context
- `setValues(tenant, user?, timezone?, origin?)`: Sets multiple context values
- `setWithToken(accessToken, secretKey, options?)`: Sets context from a JWT token
- `generateAccessToken(payload?, secret?)`: Generates a JWT token from the current context
- `bindEmitter(emitter)`: Binds an event emitter to the current context

## Types

The library exports several type definitions:

- `TENANT`, `USER`, `ACCESS_TOKEN`, etc.: Symbol constants for context keys
- `User`: User information interface
- `Installation`: Installation information interface
- `Location`: Location information interface
- `Language`: Language preference interface
- `Card`: Card profile interface

## Troubleshooting

### Context Loss in Async Operations

If you're experiencing context loss in async operations:

1. Ensure you're using `runInContext` or `runAsTenant` to wrap your async operations
2. Check that `DISABLE_ASYNC_CONTEXT` is not set to 'true'
3. Verify that you're not using a Node.js version below 20
4. Look for domain fallback warnings in your logs

```typescript
// Incorrect - context may be lost
Context.tenant = 'my-tenant';
setTimeout(() => {
  console.log(Context.tenant); // May be undefined
}, 100);

// Correct - context is preserved
await Context.runInContext({ [TENANT]: 'my-tenant' }, async () => {
  setTimeout(() => {
    console.log(Context.tenant); // Will be 'my-tenant'
  }, 100);
});
```

### JWT Token Validation Errors

If you're experiencing JWT token validation issues:

1. Check that the token hasn't expired
2. Verify you're using the correct secret key
3. Ensure the token payload contains the expected structure

### Domain Fallback Warnings

If you see domain fallback warnings in your logs:

```
Domain fallback (1): 25% [someFunction at file.js:123:45]
```

This indicates that the context is being accessed outside of an AsyncLocalStorage context. To fix:

1. Wrap your code with `runInContext` or `runAsTenant`
2. If using Express, ensure the context middleware is applied early in the middleware chain
3. Consider setting `CONTEXT_MODE=legacy` temporarily while migrating

## Migration from loopback-context

If you're migrating from loopback-context:

1. Replace imports:
   ```typescript
   // Before
   import { LoopBackContext } from 'loopback-context';

   // After
   import { Context } from '@perkd/multitenant-context';
   ```

2. Replace method calls:
   ```typescript
   // Before
   const ctx = LoopBackContext.getCurrentContext();

   // After
   const ctx = Context.getCurrentContext();
   ```

3. Set environment variable `CONTEXT_MODE=legacy` initially for backward compatibility
4. Gradually migrate to using `runInContext` and `runAsTenant` instead of domain-based approaches
5. Once migration is complete, set `CONTEXT_MODE=strict` for better tenant isolation

## License

This project is proprietary and confidential. Unauthorized copying, transferring, or reproduction of the contents of this project, via any medium, is strictly prohibited.

---

© 2025 Perkd. All rights reserved.