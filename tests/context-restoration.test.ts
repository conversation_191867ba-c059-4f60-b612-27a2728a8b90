import { describe, it, beforeEach, mock } from 'node:test'
import assert from 'node:assert'
import Context from '../src/context'
import type { ConnectionManager, Session } from '@crm/loopback'
import { TenantIsolationError } from '@perkd/errors'

type MockedFn<T extends (...args: any[]) => any> = T & {
  mock: { calls: Array<Parameters<T>> }
}

describe('Context Restoration Tests', () => {
  const createMockSession = (tenant: string): Session => ({
    tenant,
    startTransaction: mock.fn<() => Promise<void>>(),
    commitTransaction: mock.fn<() => Promise<void>>(),
    abortTransaction: mock.fn<() => Promise<void>>(),
    endSession: mock.fn<() => Promise<void>>(),
    hasActiveTransaction: () => false
  })

  const mockConnectionManager = {
    ensureConnection: mock.fn<(tenant: string) => Promise<void>>(
      async () => { /* do nothing */ }
    ) as unknown as MockedFn<ConnectionManager['ensureConnection']>,

    startSession: mock
      .fn<(tenant: string) => Promise<Session>>(async (tenant: string) =>
        createMockSession(tenant)
      ) as unknown as MockedFn<ConnectionManager['startSession']>,

    withTransaction: mock
      .fn<(_tenant: string, fn: () => Promise<void>) => Promise<void>>(
        async (_tenant: string, fn: () => Promise<void>) => {
          await fn();
        }
      ) as unknown as MockedFn<ConnectionManager['withTransaction']>,

    poolManager: {} as any,
    validationService: {} as any,
    cleanupService: {} as any,
    sessionManager: {} as any
  } as unknown as ConnectionManager

  beforeEach(() => {
    // Reset the context before each test
    Context.tenant = 'original-tenant'
    Context.accessToken = 'original-token'
    Context.user = { id: 'original-user-id', username: 'original-user' }
    Context.timezone = 'Asia/Singapore'
  })

  it('should restore original context values when an error occurs', async () => {
    // Store original context values to verify later
    const originalTenant = Context.tenant
    const originalToken = Context.accessToken
    const originalUser = Context.user
    const originalTimezone = Context.timezone

    // Attempt to run in a different tenant context, but with an error
    await assert.rejects(async () => {
      await Context.runAsTenant('error-tenant', async () => {
        // Verify the context was changed
        assert.strictEqual(Context.tenant, 'error-tenant')

        // Simulate an "Invalid token" error
        throw new Error('Invalid token')
      }, mockConnectionManager)
    }, {
      message: 'Invalid token'
    })

    // Verify the context was restored to original values
    assert.strictEqual(Context.tenant, originalTenant, 'Tenant should be restored')
    assert.strictEqual(Context.accessToken, originalToken, 'Access token should be restored')
    assert.deepStrictEqual(Context.user, originalUser, 'User should be restored')
    assert.strictEqual(Context.timezone, originalTimezone, 'Timezone should be restored')
  })

  it('should restore context even with nested tenant operations', async () => {
    // Store original context values
    const originalTenant = Context.tenant
    const originalToken = Context.accessToken

    // Run nested tenant operations with an error in the inner one
    await Context.runAsTenant('outer-tenant', async () => {
      assert.strictEqual(Context.tenant, 'outer-tenant')

      // Run an inner tenant operation that will fail
      await assert.rejects(async () => {
        await Context.runAsTenant('inner-tenant', async () => {
          assert.strictEqual(Context.tenant, 'inner-tenant')
          throw new Error('Invalid token')
        }, mockConnectionManager)
      }, {
        message: 'Invalid token'
      })

      // Verify the context was restored to the outer tenant
      assert.strictEqual(Context.tenant, 'outer-tenant',
        'Context should be restored to outer tenant after inner error')

      // Continue with outer tenant operation
      return 'outer-operation-completed'
    }, mockConnectionManager)

    // Verify the context was restored to original values
    assert.strictEqual(Context.tenant, originalTenant, 'Tenant should be restored to original')
    assert.strictEqual(Context.accessToken, originalToken, 'Access token should be restored to original')
  })

  it('should handle multiple sequential operations after an error', async () => {
    // First operation - will succeed
    await Context.runAsTenant('first-tenant', async () => {
      assert.strictEqual(Context.tenant, 'first-tenant')
      return 'success'
    }, mockConnectionManager)

    // Second operation - will fail
    await assert.rejects(async () => {
      await Context.runAsTenant('error-tenant', async () => {
        assert.strictEqual(Context.tenant, 'error-tenant')
        throw new Error('Invalid token')
      }, mockConnectionManager)
    }, {
      message: 'Invalid token'
    })

    // Third operation - should work correctly after the error
    const result = await Context.runAsTenant('third-tenant', async () => {
      assert.strictEqual(Context.tenant, 'third-tenant')
      return 'third-operation-success'
    }, mockConnectionManager)

    assert.strictEqual(result, 'third-operation-success')
  })

  it('should restore context when TenantIsolationError occurs', async () => {
    const originalTenant = Context.tenant

    // Simulate a TenantIsolationError
    await assert.rejects(async () => {
      await Context.runAsTenant('isolation-error-tenant', async () => {
        throw new TenantIsolationError('Authentication: invalid token: xyz123')
      }, mockConnectionManager)
    }, {
      message: 'Authentication: invalid token: xyz123'
    })

    // Verify context was restored
    assert.strictEqual(Context.tenant, originalTenant)

    // Verify we can run another operation successfully
    const result = await Context.runAsTenant('after-isolation-error', async () => {
      assert.strictEqual(Context.tenant, 'after-isolation-error')
      return 'success-after-isolation-error'
    }, mockConnectionManager)

    assert.strictEqual(result, 'success-after-isolation-error')
  })

  it('should handle errors in the connection manager', async () => {
    const originalTenant = Context.tenant
    const originalToken = Context.accessToken

    // Create a connection manager that throws an error during ensureConnection
    const errorConnectionManager = {
      ...mockConnectionManager,
      ensureConnection: mock.fn<(tenant: string) => Promise<void>>(
        async () => { throw new Error('Connection error') }
      ) as unknown as MockedFn<ConnectionManager['ensureConnection']>
    } as unknown as ConnectionManager

    // Attempt to run with a connection manager that will fail
    await assert.rejects(async () => {
      await Context.runAsTenant('connection-error-tenant', async () => {
        // This should not be executed because ensureConnection will fail
        assert.fail('This code should not be executed')
      }, errorConnectionManager)
    }, {
      message: 'Connection error'
    })

    // Verify the context was restored to original values
    assert.strictEqual(Context.tenant, originalTenant, 'Tenant should be restored after connection error')
    assert.strictEqual(Context.accessToken, originalToken, 'Access token should be restored after connection error')
  })

  it('should handle errors in session cleanup', async () => {
    const originalTenant = Context.tenant

    // Create a connection manager that throws during transaction cleanup
    const cleanupErrorConnectionManager = {
      ...mockConnectionManager,
      withTransaction: mock.fn<(_tenant: string, fn: () => Promise<void>) => Promise<void>>(
        async (_tenant: string, fn: () => Promise<void>) => {
          await fn();
          throw new Error('Cleanup error')
        }
      ) as unknown as MockedFn<ConnectionManager['withTransaction']>
    } as unknown as ConnectionManager

    // Run a tenant operation that will succeed, but cleanup will fail
    const result = await Context.runAsTenant('cleanup-error-tenant', async () => {
      assert.strictEqual(Context.tenant, 'cleanup-error-tenant')
      return 'operation-succeeded'
    }, cleanupErrorConnectionManager)

    // Operation should succeed despite cleanup error
    assert.strictEqual(result, 'operation-succeeded')

    // Context should be restored
    assert.strictEqual(Context.tenant, originalTenant, 'Tenant should be restored after cleanup error')
  })

  it('should handle deeply nested tenant operations with errors at different levels', async () => {
    const originalTenant = Context.tenant
    const originalToken = Context.accessToken

    // Run deeply nested tenant operations
    await Context.runAsTenant('level1-tenant', async () => {
      assert.strictEqual(Context.tenant, 'level1-tenant')

      // Level 2 - will succeed
      await Context.runAsTenant('level2-tenant', async () => {
        assert.strictEqual(Context.tenant, 'level2-tenant')

        // Level 3 - will fail
        await assert.rejects(async () => {
          await Context.runAsTenant('level3-tenant', async () => {
            assert.strictEqual(Context.tenant, 'level3-tenant')
            throw new Error('Level 3 error')
          }, mockConnectionManager)
        }, {
          message: 'Level 3 error'
        })

        // Verify context was restored to level 2
        assert.strictEqual(Context.tenant, 'level2-tenant', 'Context should be restored to level 2')
      }, mockConnectionManager)

      // Verify context was restored to level 1
      assert.strictEqual(Context.tenant, 'level1-tenant', 'Context should be restored to level 1')
    }, mockConnectionManager)

    // Verify context was restored to original
    assert.strictEqual(Context.tenant, originalTenant, 'Tenant should be restored to original')
    assert.strictEqual(Context.accessToken, originalToken, 'Access token should be restored to original')
  })

  it('should handle concurrent tenant operations', async () => {
    const originalTenant = Context.tenant

    // Run multiple tenant operations concurrently
    const results = await Promise.all([
      Context.runAsTenant('concurrent1', async () => {
        assert.strictEqual(Context.tenant, 'concurrent1')
        return 'result1'
      }, mockConnectionManager),

      Context.runAsTenant('concurrent2', async () => {
        assert.strictEqual(Context.tenant, 'concurrent2')
        return 'result2'
      }, mockConnectionManager),

      Context.runAsTenant('concurrent3', async () => {
        assert.strictEqual(Context.tenant, 'concurrent3')
        return 'result3'
      }, mockConnectionManager)
    ])

    // Verify results
    assert.deepStrictEqual(results, ['result1', 'result2', 'result3'])

    // Verify context was restored to original
    assert.strictEqual(Context.tenant, originalTenant, 'Tenant should be restored after concurrent operations')
  })

  it('should handle tenant operations with partial context', async () => {
    // Set up a partial context
    Context.tenant = 'partial-tenant'
    Context.accessToken = 'partial-token'
    Context.user = undefined
    Context.timezone = undefined

    // Store original values
    const originalTenant = Context.tenant
    const originalToken = Context.accessToken

    // Run a tenant operation
    await Context.runAsTenant('complete-tenant', async () => {
      assert.strictEqual(Context.tenant, 'complete-tenant')

      // Simulate an error
      throw new Error('Operation failed')
    }, mockConnectionManager).catch(err => {
      assert.strictEqual(err.message, 'Operation failed')
    })

    // Verify partial context was restored
    assert.strictEqual(Context.tenant, originalTenant, 'Tenant should be restored')
    assert.strictEqual(Context.accessToken, originalToken, 'Access token should be restored')
    assert.strictEqual(Context.user, undefined, 'User should remain undefined')
    assert.strictEqual(Context.timezone, undefined, 'Timezone should remain undefined')
  })

  it('should handle errors when restoring context', async () => {
    const originalTenant = Context.tenant
    const originalToken = Context.accessToken

    // Create a spy on console.error
    const originalConsoleError = console.error
    const errorMessages: string[] = []
    console.error = (message: string) => {
      errorMessages.push(message)
    }

    try {
      // Create a context with a property that will throw when set
      const problematicContext = Object.create(Context)
      Object.defineProperty(problematicContext, 'tenant', {
        get: () => originalTenant,
        set: () => { throw new Error('Cannot set tenant') }
      })

      // Run a tenant operation that will fail during context restoration
      await assert.rejects(async () => {
        await Context.runAsTenant.call(problematicContext, 'error-tenant', async () => {
          throw new Error('Operation error')
        }, mockConnectionManager)
      }, {
        message: 'Operation error'
      })

      // Verify error was logged
      assert.ok(
        errorMessages.some(msg => msg.includes('Error restoring original context')),
        'Error during context restoration should be logged'
      )
    } finally {
      // Restore console.error
      console.error = originalConsoleError
    }
  })
})
