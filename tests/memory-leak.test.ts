import { describe, it, before, after, beforeEach, afterEach } from 'node:test'
import assert from 'node:assert'
import Context from '../src/context'
import { TENANT, USER, ACCESS_TOKEN } from '../src/types'

describe('Memory Leak Prevention Tests', () => {
  let originalContextMode: string | undefined
  let originalDisableAsync: string | undefined

  before(() => {
    originalContextMode = process.env.CONTEXT_MODE
    originalDisableAsync = process.env.DISABLE_ASYNC_CONTEXT
  })

  after(() => {
    if (originalContextMode !== undefined) {
      process.env.CONTEXT_MODE = originalContextMode
    } else {
      delete process.env.CONTEXT_MODE
    }
    
    if (originalDisableAsync !== undefined) {
      process.env.DISABLE_ASYNC_CONTEXT = originalDisableAsync
    } else {
      delete process.env.DISABLE_ASYNC_CONTEXT
    }
  })

  describe('runInContext memory cleanup', () => {
    it('should clean up context in strict mode', async () => {
      process.env.CONTEXT_MODE = 'strict'
      process.env.DISABLE_ASYNC_CONTEXT = 'false'

      const testContext = {
        [TENANT]: 'test-tenant',
        [USER]: { id: 'test-user', username: 'test-user' },
        customProperty: 'test-value'
      }

      let capturedContext: any = null

      await Context.runInContext(testContext, () => {
        capturedContext = Context.getCurrentContext()
        assert.strictEqual(capturedContext[TENANT], 'test-tenant')
        assert.strictEqual(capturedContext.customProperty, 'test-value')
        assert.ok(typeof capturedContext.get === 'function')
        assert.ok(typeof capturedContext.set === 'function')
        return Promise.resolve('success')
      })

      // After runInContext completes, the enhanced context should be cleaned up
      // Note: We can't directly access the enhancedContext from outside,
      // but we can verify that the original context is not modified
      assert.strictEqual(testContext[TENANT], 'test-tenant')
      assert.strictEqual(testContext.customProperty, 'test-value')
      assert.strictEqual((testContext as any).get, undefined) // Should not have been added to original
      assert.strictEqual((testContext as any).set, undefined) // Should not have been added to original
    })

    it('should clean up context in legacy mode', async () => {
      process.env.CONTEXT_MODE = 'legacy'
      process.env.DISABLE_ASYNC_CONTEXT = 'false'

      const testContext = {
        [TENANT]: 'legacy-tenant',
        [USER]: { id: 'legacy-user', username: 'legacy-user' },
        customProperty: 'legacy-value'
      }

      let capturedContext: any = null
      let capturedDomain: any = null

      await Context.runInContext(testContext, () => {
        capturedContext = Context.getCurrentContext()
        capturedDomain = capturedContext._domain
        assert.strictEqual(capturedContext[TENANT], 'legacy-tenant')
        assert.ok(capturedDomain, 'Domain should exist in legacy mode')
        return Promise.resolve('success')
      })

      // Verify original context is not modified
      assert.strictEqual(testContext[TENANT], 'legacy-tenant')
      assert.strictEqual(testContext.customProperty, 'legacy-value')
      assert.strictEqual((testContext as any).get, undefined)
      assert.strictEqual((testContext as any).set, undefined)
      assert.strictEqual((testContext as any)._domain, undefined)
    })

    it('should handle errors and still clean up memory', async () => {
      process.env.CONTEXT_MODE = 'strict'

      const testContext = {
        [TENANT]: 'error-tenant',
        customProperty: 'error-value'
      }

      try {
        await Context.runInContext(testContext, () => {
          throw new Error('Test error')
        })
        assert.fail('Should have thrown an error')
      } catch (err: any) {
        assert.strictEqual(err.message, 'Test error')
      }

      // Even after error, original context should not be modified
      assert.strictEqual(testContext[TENANT], 'error-tenant')
      assert.strictEqual(testContext.customProperty, 'error-value')
      assert.strictEqual((testContext as any).get, undefined)
      assert.strictEqual((testContext as any).set, undefined)
    })

    it('should clean up multiple nested contexts', async () => {
      process.env.CONTEXT_MODE = 'strict'

      const outerContext = {
        [TENANT]: 'outer-tenant',
        level: 'outer'
      }

      const innerContext = {
        [TENANT]: 'inner-tenant',
        level: 'inner'
      }

      await Context.runInContext(outerContext, async () => {
        assert.strictEqual(Context.tenant, 'outer-tenant')

        await Context.runInContext(innerContext, () => {
          assert.strictEqual(Context.tenant, 'inner-tenant')
          return Promise.resolve()
        })

        // Should return to outer context
        assert.strictEqual(Context.tenant, 'outer-tenant')
      })

      // Both contexts should remain unmodified
      assert.strictEqual((outerContext as any).get, undefined)
      assert.strictEqual((outerContext as any).set, undefined)
      assert.strictEqual((innerContext as any).get, undefined)
      assert.strictEqual((innerContext as any).set, undefined)
    })
  })

  describe('releaseContext memory cleanup', () => {
    it('should handle context without domain', () => {
      const context = {
        [TENANT]: 'test-tenant',
        get: () => 'test',
        set: () => {},
        customProperty: 'test'
      }

      // Should not throw when releasing context without domain
      assert.doesNotThrow(() => {
        Context.releaseContext(context)
      })

      // Function references should be cleaned up
      assert.strictEqual(context.get, undefined)
      assert.strictEqual(context.set, undefined)
      // Other properties should remain
      assert.strictEqual(context[TENANT], 'test-tenant')
      assert.strictEqual(context.customProperty, 'test')
    })

    it('should handle null/undefined context gracefully', () => {
      assert.doesNotThrow(() => {
        Context.releaseContext(null as any)
        Context.releaseContext(undefined as any)
      })
    })

    it('should clean up domain references', () => {
      const domain = require('node:domain').create()
      const context = {
        [TENANT]: 'domain-tenant',
        _domain: domain,
        get: () => 'test',
        set: () => {}
      }

      domain._loopbackContext = context

      Context.releaseContext(context)

      // Domain references should be cleaned up
      assert.strictEqual(context._domain, undefined)
      assert.strictEqual(domain._loopbackContext, undefined)
      assert.strictEqual(context.get, undefined)
      assert.strictEqual(context.set, undefined)
    })
  })

  describe('Memory stress test', () => {
    it('should handle many sequential context operations without memory buildup', async () => {
      process.env.CONTEXT_MODE = 'strict'

      const iterations = 1000
      const results: string[] = []

      for (let i = 0; i < iterations; i++) {
        const context = {
          [TENANT]: `tenant-${i}`,
          iteration: i,
          data: `data-${i}`
        }

        const result = await Context.runInContext(context, () => {
          return `result-${Context.tenant}-${i}`
        })

        results.push(result)

        // Verify context is properly isolated
        assert.strictEqual(result, `result-tenant-${i}-${i}`)
      }

      assert.strictEqual(results.length, iterations)

      // All contexts should have been cleaned up
      // We can't directly measure memory, but we can verify no lingering references
      assert.ok(true, 'Completed stress test without errors')
    })

    it('should handle concurrent context operations', async () => {
      process.env.CONTEXT_MODE = 'strict'

      const concurrentOperations = 100
      const promises: Promise<string>[] = []

      for (let i = 0; i < concurrentOperations; i++) {
        const promise = Context.runInContext({
          [TENANT]: `concurrent-${i}`,
          operationId: i
        }, async () => {
          // Simulate some async work
          await new Promise(resolve => setTimeout(resolve, Math.random() * 10))
          return `concurrent-result-${Context.tenant}-${i}`
        })

        promises.push(promise)
      }

      const results = await Promise.all(promises)

      assert.strictEqual(results.length, concurrentOperations)

      // Verify each result is correct
      results.forEach((result, index) => {
        assert.strictEqual(result, `concurrent-result-concurrent-${index}-${index}`)
      })
    })
  })

  describe('Domain cleanup edge cases', () => {
    it('should handle domain errors gracefully', async () => {
      process.env.CONTEXT_MODE = 'legacy'

      const testContext = {
        [TENANT]: 'error-domain-tenant',
        testData: 'error-test'
      }

      // Test that domain errors don't prevent cleanup
      // We'll test this without actually triggering the async error to avoid test pollution
      await Context.runInContext(testContext, () => {
        const currentContext = Context.getCurrentContext()
        const domain = currentContext?._domain

        // Verify domain exists but don't trigger async error
        assert.ok(domain, 'Domain should exist in legacy mode')

        return Promise.resolve('completed')
      })

      // Context should still be cleaned up
      assert.strictEqual((testContext as any).get, undefined)
      assert.strictEqual((testContext as any).set, undefined)
      assert.strictEqual((testContext as any)._domain, undefined)
    })

    it('should clean up circular references between domain and context', async () => {
      process.env.CONTEXT_MODE = 'legacy'

      const testContext = {
        [TENANT]: 'circular-tenant',
        circularRef: null as any
      }

      await Context.runInContext(testContext, () => {
        const currentContext = Context.getCurrentContext()
        // Create a circular reference
        testContext.circularRef = currentContext
        return Promise.resolve()
      })

      // The circular reference will remain but the enhanced context should be cleaned up
      // We can't directly test the circular reference cleanup, but we can verify
      // that the original context doesn't have the enhanced properties
      assert.strictEqual((testContext as any).get, undefined)
      assert.strictEqual((testContext as any).set, undefined)
      assert.strictEqual((testContext as any)._domain, undefined)

      // The circularRef will point to the enhanced context, not the original
      assert.ok(testContext.circularRef !== null)
      assert.strictEqual(testContext.circularRef[TENANT], 'circular-tenant')
    })

    it('should handle multiple domain cleanup attempts', () => {
      const domain = require('node:domain').create()
      const context = {
        [TENANT]: 'multi-cleanup-tenant',
        _domain: domain,
        get: () => 'test',
        set: () => {}
      }

      domain._loopbackContext = context

      // Multiple cleanup calls should not throw
      assert.doesNotThrow(() => {
        Context.releaseContext(context)
        Context.releaseContext(context) // Second call should be safe
        Context.releaseContext(context) // Third call should be safe
      })

      assert.strictEqual(context._domain, undefined)
      assert.strictEqual(context.get, undefined)
      assert.strictEqual(context.set, undefined)
    })
  })

  describe('Function reference cleanup', () => {
    it('should prevent closure memory leaks from get/set functions', async () => {
      process.env.CONTEXT_MODE = 'strict'

      let capturedGetFunction: Function | null = null
      let capturedSetFunction: Function | null = null

      await Context.runInContext({
        [TENANT]: 'closure-test-tenant',
        testData: 'closure-data'
      }, () => {
        const currentContext = Context.getCurrentContext()
        capturedGetFunction = currentContext?.get || null
        capturedSetFunction = currentContext?.set || null

        // Verify functions work during execution
        assert.ok(typeof capturedGetFunction === 'function')
        assert.ok(typeof capturedSetFunction === 'function')

        return Promise.resolve()
      })

      // After cleanup, the captured functions should still exist but the context
      // they reference should be cleaned up. We can't directly test this,
      // but we can verify the functions were captured
      assert.ok(typeof capturedGetFunction === 'function')
      assert.ok(typeof capturedSetFunction === 'function')
    })

    it('should clean up custom function properties', () => {
      const customFunction = () => 'custom'
      const context = {
        [TENANT]: 'custom-func-tenant',
        get: () => 'test',
        set: () => {},
        customFunc: customFunction,
        normalProperty: 'normal'
      }

      Context.releaseContext(context)

      // get and set should be cleaned up
      assert.strictEqual(context.get, undefined)
      assert.strictEqual(context.set, undefined)

      // Custom function and normal properties should remain
      assert.strictEqual(context.customFunc, customFunction)
      assert.strictEqual(context.normalProperty, 'normal')
      assert.strictEqual(context[TENANT], 'custom-func-tenant')
    })
  })
})
