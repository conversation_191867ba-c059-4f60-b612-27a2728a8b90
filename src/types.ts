import { Wallet, User as Wallet_User } from '@perkd/wallet'

/**
 * Used between services (both C & X) and calls from Staff Card applets
 * @example:
 *		"id": "636c825f89d606006b5e380f",
	*		"username": "<PERSON>",
	*		"email": "<EMAIL>",
	*		"staffId": "636c825f55c5b6afa1f912a1",
	*		"emailVerified": true,
	*		"roles": [ "admin" ]
	*/
export interface CRM_User {
	id: string
	username: string
	email?: string
	staffId?: string,
	emailVerified?: boolean
	roles?: string[]
}

export type User = CRM_User | Wallet_User

export type Card = {
	id: string
}

/** 
 * @example: Installation {
 *   "id": "D8DCEB49-EA47-476F-B080-xxxxxxxxxxxx",
 *   "app": {
 *     "id": "me.perkd.dev",
 *     "version": "5.3.0",
 *     "env": "dev"
 *   },
 *   "device": {
 *     "uuid": "D8DCEB49-EA47-476F-B080-xxxxxxxxxxxx",
 *     "name": "iPhone",
 *     "brand": "Apple",
 *     "manufacturer": "Apple",
 *     "model": "iPhone 13 mini",
 *     "ip": "*************"
 *   },
 *   "locale": {
 *     "languages": [
 *       "en-SG",
 *       "zh-Hans-SG",
 *       "zh-Hant-SG",
 *       "ja-SG",
 *       "ms-SG"
 *     ],
 *     "currency": "SGD",
 *     "country": "SG",
 *     "timeZone": "Asia/Singapore",
 *     "numberFormat": {
 *       "decimalSeparator": ".",
 *       "groupingSeparator": ","
 *     },
 *     "useMetric": true,
 *     "temperature": "celsius",
 *     "calendar": "gregorian"
 *   },
 *   "os": {
 *     "name": "ios",
 *     "version": "16.3.1"
 *   }
 * }
*/
export type Installation = Wallet.Installation & {
	userId: string
	personId: string
	accountId: string
}

/**
 * @example Location: {
 *   "formatted": "Northshore Bungalows, 245 Ponggol 17th Ave, Singapore 829703",
 *   "house": "245",
 *   "street": "Ponggol 17th Ave",
 *   "postCode": "829703",
 *   "country": "SG",
 *   "geo": {
 *     "coordinates": [
 *       103.**************,
 *       1.****************
 *     ]
 *   },
 *   "coords": {
 *     "lng": 103.**************,
 *     "lat": 1.****************
 *   },
 *   "name": "Northshore Bungalows",
 *   "short": "245 Ponggol 17th Ave",
 *   "places": [
 *     {
 *       "id": "62ea8878bd9b27001d036cf0",
 *       "name": "Oasis Terraces",
 *       "brand": {
 *        "short": "",
 *         "long": "",
 *         "color": null,
 *         "logo": null
 *       },
 *       "geo": {
 *         "type": "Point",
 *         "coordinates": [
 *           103.9039623,
 *           1.4162193
 *         ]
 *       },
 *       "distance": ********,
 *       "address": "247 Ponggol Seventeenth Ave, Singapore 829704"
 *     }
 *   ]
 * }
*/
export type Location = Wallet.Location
export type Language = string		// eg. zh-hant-TW

export type JwtPayload = {
	tenant: {
		code: string
		timezone?: string
	}
	user?: User
	iat?: number
	exp?: number
}

export type Payload = {
	tenant: string
	user?: User
	exp?: number
	timezone?: string
}

export const TRAP = 'trap'			// when tenant missing
export const SERVICE = 'service'	// default context on boot, used by service

export const
	ACCESS_TOKEN = 'access_token',
	TENANT = 'tenant-code',
	USER = 'tenant-user',
	INSTALL = 'perkd-install',
	LOCATION = 'perkd-location',
	CARD = 'card',			// staff card
	TIMEZONE = 'timezone',	// eventbus module
	ORIGIN = 'origin',
	LANGUAGE = 'language',	// applet requests (mainly)
	IDEMPOTENCY_KEY = 'idempotency-key',

	// to deprecate
	USER_AGENT = 'user_agent',
	REFERER = 'referer',
	CLIENT_IP = 'client_ip'
