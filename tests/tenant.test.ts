import { describe, it, beforeEach, mock } from 'node:test'
import assert from 'node:assert'
import Context from '../src/context'
import type { ConnectionManager, Session } from '@crm/loopback'

type MockedFn<T extends (...args: any[]) => any> = T & {
  mock: { calls: Array<Parameters<T>> }
}

describe('Tenant Isolation', () => {
  const createMockSession = (tenant: string): Session => ({
    tenant,
    startTransaction: mock.fn<() => Promise<void>>(),
    commitTransaction: mock.fn<() => Promise<void>>(),
    abortTransaction: mock.fn<() => Promise<void>>(),
    endSession: mock.fn<() => Promise<void>>(),
    hasActiveTransaction: () => false
  })

  const mockConnectionManager = {
    ensureConnection: mock.fn<(tenant: string) => Promise<void>>(
      async () => { /* do nothing */ }
    ) as unknown as MockedFn<ConnectionManager['ensureConnection']>,

    startSession: mock
      .fn<(tenant: string) => Promise<Session>>(async (tenant: string) =>
        createMockSession(tenant)
      ) as unknown as MockedFn<ConnectionManager['startSession']>,

    withTransaction: mock
      .fn<(_tenant: string, fn: () => Promise<void>) => Promise<void>>(
        async (_tenant: string, fn: () => Promise<void>) => {
          await fn();
        }
      ) as unknown as MockedFn<ConnectionManager['withTransaction']>,

    poolManager: {} as any,
    validationService: {} as any,
    cleanupService: {} as any,
    sessionManager: {} as any

  } as unknown as ConnectionManager

  beforeEach(() => {
    mockConnectionManager.withTransaction = mock
      .fn<(_tenant: string, fn: () => Promise<void>) => Promise<void>>(
        async (_tenant: string, fn: () => Promise<void>) => {
          await fn()
        }
      ) as unknown as MockedFn<ConnectionManager['withTransaction']>
  })

  it('should execute in tenant context', async () => {
    await Context.runAsTenant('iso-tenant', () => {
      assert.strictEqual(Context.tenant, 'iso-tenant')
    }, mockConnectionManager)
  })

  it('should clean up sessions', async () => {
    await assert.rejects(async () => {
      await Context.runAsTenant('cleanup-tenant', async () => {
        throw new Error('Simulated failure')
      }, mockConnectionManager)
    }, {
      message: 'Simulated failure'
    })

    // Confirm that, even when an error occurs, withTransaction was called for cleanup
    assert.strictEqual((mockConnectionManager.withTransaction as any).mock.calls.length, 1)
  })

  it('should handle tenant isolation', async () => {
    const isolationMock = {
      ensureConnection: mock.fn<(tenant: string) => Promise<void>>(
        async () => { /* do nothing */ }
      ) as unknown as MockedFn<ConnectionManager['ensureConnection']>,

      startSession: mock.fn(async (tenant: string) =>
        createMockSession(tenant)
      ) as unknown as MockedFn<ConnectionManager['startSession']>,

      withTransaction: mock.fn(async (_tenant: string, fn: () => Promise<void>) => {
        await fn();
      }) as unknown as MockedFn<ConnectionManager['withTransaction']>
    } as unknown as ConnectionManager;

    await Context.runAsTenant('test-tenant', async () => {
      /* test logic */
    }, isolationMock)

    assert.strictEqual((isolationMock.withTransaction as any).mock.calls.length, 1)
  })

  it('should track transaction calls', async () => {
    await Context.runAsTenant('test-tenant', async () => {
      // Test logic here
    }, mockConnectionManager)

    assert.strictEqual((mockConnectionManager.withTransaction as any).mock.calls.length, 1)
  })
})