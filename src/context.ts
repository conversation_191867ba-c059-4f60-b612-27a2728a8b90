import { AsyncLocalStorage } from 'node:async_hooks'
import domain from 'node:domain'
import { Session, ConnectionManager } from '@crm/loopback'
import { User as Wallet_User } from '@perkd/wallet'
import { Security, shortId } from '@perkd/utils'
import { TenantIsolationError } from '@perkd/errors'
import { EXTRACT_CALLER } from './utils'
import { ACCESS_TOKEN, TENANT, USER, TIMEZONE, ORIGIN, TRAP, INSTALL, LANGUAGE, LOCATION, CARD, CRM_User, IDEMPOTENCY_KEY } from './types'
import { User, Installation, Location, Language, Card, Payload, JwtPayload } from './types'

// environment variables
const {
	CONTEXT_MODE,
	DISABLE_ASYNC_CONTEXT,
	PERKD_SECRET_KEY = 'perkd-secret-key'
} = process.env

declare module 'node:domain' {
	interface Domain {
		_loopbackContext?: ContextType
	}
}

declare global {
	namespace NodeJS {
		interface Process {
			domain?: domain.Domain
		}
	}
}

interface ContextType {
	[key: string]: any
	[TENANT]?: string
	[ACCESS_TOKEN]?: string
	[USER]?: User
	[TIMEZONE]?: string
	[ORIGIN]?: string
	[IDEMPOTENCY_KEY]?: string
	[INSTALL]?: Installation
	[CARD]?: Card
	[LOCATION]?: Location
	[LANGUAGE]?: Language
	tenantConfig?: Record<string, any>
	_domain?: domain.Domain
	get?: (key: string) => any
	set?: (key: string, value: any) => any
}

interface Metrics {
	domainFallbacks: number
	strictAccess: number
	total: number
}

const { Jwt } = Security,
	ENGLISH = 'en',
	EXPIRY = 'exp'		// jwt

class MultitenantContext {
	private static _instance: MultitenantContext

	private als: AsyncLocalStorage<ContextType>
	private activeDomains: WeakMap<domain.Domain, boolean>
	public metrics: Metrics
	public enabled: boolean
	private _activeContextsRegistry?: Set<ContextType>

	static withContext(context: ContextType, fn: () => any): any {
		const instance = new MultitenantContext()
		return instance.runInContext(context, fn)
	}

	constructor() {
		if (MultitenantContext._instance) {
			return MultitenantContext._instance
		}

		this.als = new AsyncLocalStorage<ContextType>()
		this.activeDomains = new WeakMap()
		this.metrics = {
			domainFallbacks: 0,
			strictAccess: 0,
			total: 0
		}
		this.enabled = DISABLE_ASYNC_CONTEXT !== 'true'

		MultitenantContext._instance = Object.freeze(this) as unknown as MultitenantContext
	}

	// ---  loopback-context compatibility

	createContext(initialValue: ContextType = {}): ContextType {
		const { requestId = this.generateRequestId() } = initialValue

		return {
			...initialValue,
			requestId,
			get: (key: string) => this.get(key),
			set: (key: string, value: any) => this.set(key, value),
			_domain: this._createDomain(initialValue)
		}
	}

	releaseContext(context: ContextType): void {
		if (!context) return;

		try {
			// Clean up domain-specific resources if domain exists
			if (context._domain) {
				// Remove from active domains map
				this.activeDomains.delete(context._domain);

				// Remove all listeners, not just error
				context._domain.removeAllListeners();

				// Break circular references if any exist
				if (context._domain._loopbackContext === context) {
					delete context._domain._loopbackContext;
				}

				// Remove domain reference from context
				delete context._domain;
			}

			// Clean up function references that could cause memory leaks
			// These create closures that capture 'this' and prevent garbage collection
			if (context.get) delete context.get;
			if (context.set) delete context.set;

			Object.keys(context).forEach(key => {
				if (key !== TENANT.toString()) { // Preserve tenant symbol if needed elsewhere
					delete context[key]
				}
			})
			// Clean up any MongoDB connections or other resources
			// that might be attached to this context

			// Optional debug logging
			// if (context.requestId) {
			// 	console.debug(`Released context for request ${context.requestId}`);
			// }
		} catch (err) {
			console.error('Error releasing context:', err);
		}
	}

	getCurrentContext(): ContextType | undefined {
		const { metrics } = this,
			ctx = this.als.getStore()

		metrics.total++

		if (ctx) {
			metrics.strictAccess++
			return ctx
		}

		const { stack = '' } = new Error(),
			match = EXTRACT_CALLER.exec(stack),
			caller = match ? `[${match?.[1]} at ${match?.[2]}]` : stack

		// match[1] is the extracted function name
		// match[2] is the extracted file, line, and column information

		metrics.domainFallbacks++
		console.warn(`Domain fallback (${metrics.domainFallbacks}): ${metrics.domainFallbacks / metrics.total * 100}% ${caller}`)
		return process.domain?._loopbackContext || {}
	}

	get(key: string): any {
		return this.context?.[key]
	}

	set(key: string, value: any): any {
		if (this.context) {
			this.context[key] = value
			return value
		}
	}

	// ---  accessors

	private get context(): ContextType | undefined {
		return this.getCurrentContext()
	}

	get tenant(): string {
		return this.context?.[TENANT] || TRAP
	}

	set tenant(code: string) {
		if (this.context) {
			this.context[TENANT] = code.toLowerCase()
		}
	}

	get accessToken(): string {
		return this.context?.[ACCESS_TOKEN] || ''
	}

	set accessToken(token: string) {
		if (this.context) {
			this.context[ACCESS_TOKEN] = token
		}
	}

	get timezone(): string | undefined {
		return this.context?.[TIMEZONE]
	}

	set timezone(timezone: string | undefined) {
		if (this.context) {
			this.context[TIMEZONE] = timezone
		}
	}

	get user(): User | undefined {
		return this.context?.[USER]
	}

	set user(user: User | undefined) {
		if (this.context) {
			this.context[USER] = user
		}
	}

	get origin(): string | undefined {
		return this.context?.[ORIGIN]
	}

	set origin(origin: string) {
		if (this.context) {
			this.context[ORIGIN] = origin
		}
	}

	get idempotencyKey() {
		return this.get(IDEMPOTENCY_KEY) || shortId()
	}

	set idempotencyKey(value: string) {
		if (this.context) {
			this.context[IDEMPOTENCY_KEY] = value
		}
	}

	//  Perkd App specific

	get appContext() {
		const { location: loc, user, cardProfile: card, installation } = this,
			{ spot } = loc ?? {},
			{ id: userId, staffId, username: userName } = <CRM_User>user ?? {},
			staff = staffId ? { id: staffId, userId, userName } : undefined,
			{ id, type, name } = spot ?? {},
			location = { id, type, name }

		return { staff, user, card, location, installation }
	}

	get accountId(): string | null {
		const { accountId = null } = this.user as Wallet_User ?? {}
		return accountId
	}

	/**
	 * Get installation & user from Perkd App
	 * @return	{Object} { ...install, ...user }
	 * 1. install - decoded JWT perkd-install header. (see installation/docs/perkd-install.json)
	 * 2. user - user object in decoded JWT Authorization header. (see installation/docs/Authorization.json)
	 */
	get installation(): Installation | {} {
		const { user } = this,
			install = this.get(INSTALL),
			{ id: userId } = user as CRM_User ?? {},
			{ personId, accountId } = user as Wallet_User ?? {}

		if (!install) return {}
		if (!install.id) {
			const err = new Error('header_missing_install')
			console.error('getInstall', { install, userId, personId, accountId, err })
		}

		// CRM: inject userId (personId & accountId N.A) (placeId already in install)
		// Perkd: inject personId, accountId (userId N.A)
		return { ...install, userId, personId, accountId }
	}

	set installation(installation: Installation) {
		if (this.context) {
			this.context[INSTALL] = installation
		}
	}

	get cardProfile(): Card {
		return this.get(CARD) || {}
	}

	set cardProfile(card: Card) {
		if (this.context) {
			this.context[CARD] = card
		}
	}

	get location(): Location {
		return this.get(LOCATION) || {}
	}

	set location(location: Location) {
		if (this.context) {
			this.context[LOCATION] = location
		}
	}

	get language(): Language {
		return this.get(LANGUAGE) || ENGLISH
	}

	set language(language: Language) {
		if (this.context) {
			this.context[LANGUAGE] = language
		}
	}

	// ---  Extended methods

	/**
	 * Set the context values
	 * @param tenant - tenant code
	 * @param [user] - CRM user or App user
	 * @param [timezone] - timezone of tenant
	 * @param [origin] - origin of request
	 */
	setValues(tenant: string, user?: User, timezone?: string, origin?: string) {
		this.tenant = tenant
		this.user = user
		this.timezone = timezone
		if (origin) this.origin = origin
	}

	/**
	 * Set the context with the token, stored in the context:
	 * 	tenant - tenant code
	 * 	user - username
	 * 	timezone - timezone of tenant
	 * 	exp - expiration of token
	 * @param accessToken - jwt token
	 * @param secretKey - jwt secret key
	 * @param options - allowExpired
	 * @returns - payload or error
	 */
	setWithToken(accessToken: string, secretKey: string, options: any = {}): Payload | Error {
		if (!accessToken) {
			return new TenantIsolationError('Authentication: failed to get token')
		}

		this.accessToken = accessToken
		let decodedJWT

		try {
			const jwt = new Jwt(secretKey)

			if (jwt.verify(accessToken)) {
				decodedJWT = jwt.decode(accessToken)
			}
			else {
				return new TenantIsolationError('Authentication: invalid token: ' + accessToken)
			}
		}
		catch (err: any) {
			return new TenantIsolationError(`Authentication error: ${err.message}`)
		}

		try {
			const { allowExpired } = options,
				payload: JwtPayload = (typeof decodedJWT.payload === 'string')
					? JSON.parse(decodedJWT.payload)
					: decodedJWT.payload,
				{ user, exp } = payload,
				{ code = '', timezone } = payload.tenant ?? {},
				tenant = code.toLowerCase(),
				result = { tenant, user, timezone, exp }

			if (!allowExpired && payload[EXPIRY]) {
				const now = Math.floor(Date.now() / 1000)

				if (payload[EXPIRY] <= now) {
					return new TenantIsolationError('Authentication: token has expired')
				}
			}
			if (!tenant) {
				return new TenantIsolationError('Authentication: missing Tenant Code')
			}

			this.setValues(tenant, user, timezone)
			return result
		}
		catch (err: any) {
			return new TenantIsolationError(`Token parsing error: ${err.message}`)
		}
	}

	bindEmitter(emitter: NodeJS.EventEmitter): void {
		const d = process.domain
		if (d && d._loopbackContext) {
			d.add(emitter)
		}
	}

	generateAccessToken(payload: any = {}, secret = PERKD_SECRET_KEY): string {
		if (!payload.tenant) {
			payload.tenant = {
				code: this.tenant,
				timezone: this.timezone
			}
		}
		const jwt = new Jwt(secret)
		return jwt.encode(payload)
	}

	generateRequestId(): string {
		return Date.now().toString(36) + Math.random().toString(36).slice(2)
	}

	// ---  Execution in Context

	async runInContext(context: ContextType, fn: () => any): Promise<any> {
		// Validate tenant exists using Symbol key
		if (CONTEXT_MODE === 'strict' && !context[TENANT]) {
			throw new TenantIsolationError('Missing tenant in context')
		}

		const enhancedContext: ContextType = {
			[TENANT]: context[TENANT],		// Use Symbol key
			...context,
			get: (key: string) => this.get(key),
			set: (key: string, value: any) => this.set(key, value)
		}

		try {
			if (CONTEXT_MODE === 'strict') {
				return this.als.run(enhancedContext, () => {
					enhancedContext[TENANT] = context[TENANT]
					return fn()
				})
			}

			// Legacy domain-based handling
			const d = this._createDomain(enhancedContext)
			enhancedContext._domain = d

			return await this.als.run(enhancedContext, () => d.run(() => {
				d._loopbackContext = enhancedContext
				return fn()
			}))
		}
		catch (err) {
			console.error('Error in runInContext:', err)
			throw err
		}
	}

	async runAsTenant(tenant: string | Record<string, any>, fn: () => any, connectionManager?: ConnectionManager, options: Record<string, any> = {}): Promise<any> {
		if (!tenant) {
			throw new TenantIsolationError('runAsTenant: missing Tenant Code')
		}

		const baseContext = this.context || {},
			tenantConfig = typeof tenant === 'string'
				? { id: tenant, ...options }
				: tenant,
			tenantCode = tenantConfig.id,
			ctx: ContextType = {
				...baseContext,
				[TENANT]: tenantCode,
				tenantConfig: { ...baseContext.tenantConfig, ...tenantConfig }
			}

		// Store original context values to restore in case of error
		const originalContext = {
			tenant: this.tenant,
			accessToken: this.accessToken,
			user: this.user,
			timezone: this.timezone
		}

		try {
			return await this.runInContext(ctx, async () => {
				let session: Session | null = null

				try {
					if (connectionManager) {
						await connectionManager.ensureConnection(tenantCode)
						session = await connectionManager.startSession(tenantCode)
					}
					return await fn()
				}
				catch (err) {
					console.error('Error during tenant operation:', err)
					throw err
				}
				finally {
					if (session) {
						try {
							await connectionManager!.withTransaction(tenantCode, async () => {
								if (session!.hasActiveTransaction?.()) {
									await session!.abortTransaction?.()
								}
							})
						}
						catch (err) {
							console.error('Error cleaning up session:', err)
						}
					}
				}
			})
		}
		catch (err) {
			// Restore original context values on error
			try {
				this.tenant = originalContext.tenant
				this.accessToken = originalContext.accessToken
				this.user = originalContext.user
				this.timezone = originalContext.timezone
			} catch (restoreErr) {
				console.error('Error restoring original context:', restoreErr)
			}

			throw err
		}
	}

	/**
	 * Execute with role privileges
	 * @param user
	 * @param role
	 * @param fn
	 * @param tenantCode
	 */
	async runWithPrivilege(user: User, role: string, fn: () => any, tenantCode?: string): Promise<any> {
		const { accessToken: oToken, tenant: oTenant } = this

		try {
			const payload = {
				user: { ...user, roles: [role] },
				tenant: { code: tenantCode || oTenant }
			},
				token = this.generateAccessToken(payload)

			if (tenantCode) {
				this.tenant = tenantCode
				// FIXME await cacheDataSource(app, code)
			}
			this.accessToken = token

			const res = await fn()
			return res
		}
		catch (err) {
			throw err instanceof Error ? err : new Error(String(err))
		}
		finally {
			this.accessToken = oToken
			if (tenantCode && oTenant) this.tenant = oTenant
		}
	}

	private _createDomain(context: ContextType): domain.Domain {
		const d = domain.create()
		d._loopbackContext = context
		this.activeDomains.set(d, true)
		d.on('error', (err) => this._handleDomainError(err, d))
		return d
	}

	private _handleDomainError(err: Error, d: domain.Domain): void {
		console.error('Domain error:', err)
		this.activeDomains.delete(d)
	}

	trackActiveContext(context: ContextType): void {
		// Keep track of contexts in a Set or Array
		if (!this._activeContextsRegistry) {
			this._activeContextsRegistry = new Set<ContextType>();
		}
		this._activeContextsRegistry.add(context);
	}

	releaseAllContexts(): void {
		// If we're tracking contexts separately
		if (this._activeContextsRegistry) {
			const contexts = Array.from(this._activeContextsRegistry);
			contexts.forEach(context => {
				this.releaseContext(context);
				this._activeContextsRegistry?.delete(context);
			});
		}

		console.info('All active contexts released');
	}
}

const Context = new MultitenantContext()
export default Context
