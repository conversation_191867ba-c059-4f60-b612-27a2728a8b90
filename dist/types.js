"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CLIENT_IP = exports.REFERER = exports.USER_AGENT = exports.IDEMPOTENCY_KEY = exports.LANGUAGE = exports.ORIGIN = exports.TIMEZONE = exports.CARD = exports.LOCATION = exports.INSTALL = exports.USER = exports.TENANT = exports.ACCESS_TOKEN = exports.SERVICE = exports.TRAP = void 0;
exports.TRAP = 'trap'; // when tenant missing
exports.SERVICE = 'service'; // default context on boot, used by service
exports.ACCESS_TOKEN = 'access_token', exports.TENANT = 'tenant-code', exports.USER = 'tenant-user', exports.INSTALL = 'perkd-install', exports.LOCATION = 'perkd-location', exports.CARD = 'card', exports.TIMEZONE = 'timezone', exports.ORIGIN = 'origin', exports.LANGUAGE = 'language', exports.IDEMPOTENCY_KEY = 'idempotency-key', 
// to deprecate
exports.USER_AGENT = 'user_agent', exports.REFERER = 'referer', exports.CLIENT_IP = 'client_ip';
//# sourceMappingURL=types.js.map