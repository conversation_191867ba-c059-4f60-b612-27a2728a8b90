import domain from 'node:domain';
import { ConnectionManager } from '@crm/loopback';
import { ACCESS_TOKEN, TENANT, USER, TIMEZONE, ORIGIN, INSTALL, LANGUAGE, LOCATION, CARD, IDEMPOTENCY_KEY } from './types';
import { User, Installation, Location, Language, Card, Payload } from './types';
declare module 'node:domain' {
    interface Domain {
        _loopbackContext?: ContextType;
    }
}
declare global {
    namespace NodeJS {
        interface Process {
            domain?: domain.Domain;
        }
    }
}
interface ContextType {
    [key: string]: any;
    [TENANT]?: string;
    [ACCESS_TOKEN]?: string;
    [USER]?: User;
    [TIMEZONE]?: string;
    [ORIGIN]?: string;
    [IDEMPOTENCY_KEY]?: string;
    [INSTALL]?: Installation;
    [CARD]?: Card;
    [LOCATION]?: Location;
    [LANGUAGE]?: Language;
    tenantConfig?: Record<string, any>;
    _domain?: domain.Domain;
    get?: (key: string) => any;
    set?: (key: string, value: any) => any;
}
interface Metrics {
    domainFallbacks: number;
    strictAccess: number;
    total: number;
}
declare class MultitenantContext {
    private static _instance;
    private als;
    private activeDomains;
    metrics: Metrics;
    enabled: boolean;
    private _activeContextsRegistry?;
    static withContext(context: ContextType, fn: () => any): any;
    constructor();
    createContext(initialValue?: ContextType): ContextType;
    releaseContext(context: ContextType): void;
    getCurrentContext(): ContextType | undefined;
    get(key: string): any;
    set(key: string, value: any): any;
    private get context();
    get tenant(): string;
    set tenant(code: string);
    get accessToken(): string;
    set accessToken(token: string);
    get timezone(): string | undefined;
    set timezone(timezone: string | undefined);
    get user(): User | undefined;
    set user(user: User | undefined);
    get origin(): string | undefined;
    set origin(origin: string);
    get idempotencyKey(): string;
    set idempotencyKey(value: string);
    get appContext(): {
        staff: {
            id: string;
            userId: string;
            userName: string;
        } | undefined;
        user: User | undefined;
        card: Card;
        location: {
            id: string | undefined;
            type: string;
            name: string;
        };
        installation: {} | Installation;
    };
    get accountId(): string | null;
    /**
     * Get installation & user from Perkd App
     * @return	{Object} { ...install, ...user }
     * 1. install - decoded JWT perkd-install header. (see installation/docs/perkd-install.json)
     * 2. user - user object in decoded JWT Authorization header. (see installation/docs/Authorization.json)
     */
    get installation(): Installation | {};
    set installation(installation: Installation);
    get cardProfile(): Card;
    set cardProfile(card: Card);
    get location(): Location;
    set location(location: Location);
    get language(): Language;
    set language(language: Language);
    /**
     * Set the context values
     * @param tenant - tenant code
     * @param [user] - CRM user or App user
     * @param [timezone] - timezone of tenant
     * @param [origin] - origin of request
     */
    setValues(tenant: string, user?: User, timezone?: string, origin?: string): void;
    /**
     * Set the context with the token, stored in the context:
     * 	tenant - tenant code
     * 	user - username
     * 	timezone - timezone of tenant
     * 	exp - expiration of token
     * @param accessToken - jwt token
     * @param secretKey - jwt secret key
     * @param options - allowExpired
     * @returns - payload or error
     */
    setWithToken(accessToken: string, secretKey: string, options?: any): Payload | Error;
    bindEmitter(emitter: NodeJS.EventEmitter): void;
    generateAccessToken(payload?: any, secret?: string): string;
    generateRequestId(): string;
    runInContext(context: ContextType, fn: () => any): Promise<any>;
    runAsTenant(tenant: string | Record<string, any>, fn: () => any, connectionManager?: ConnectionManager, options?: Record<string, any>): Promise<any>;
    /**
     * Execute with role privileges
     * @param user
     * @param role
     * @param fn
     * @param tenantCode
     */
    runWithPrivilege(user: User, role: string, fn: () => any, tenantCode?: string): Promise<any>;
    private _createDomain;
    private _handleDomainError;
    trackActiveContext(context: ContextType): void;
    releaseAllContexts(): void;
}
declare const Context: MultitenantContext;
export default Context;
